// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		AEAB9F982E4894180091B83E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = AEAB9F802E4894160091B83E /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AEAB9F872E4894160091B83E;
			remoteInfo = "location-demo";
		};
		AEAB9FA22E4894180091B83E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = AEAB9F802E4894160091B83E /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AEAB9F872E4894160091B83E;
			remoteInfo = "location-demo";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		AEAB9F882E4894160091B83E /* location-demo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "location-demo.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		AEAB9F972E4894180091B83E /* location-demoTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "location-demoTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		AEAB9FA12E4894180091B83E /* location-demoUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "location-demoUITests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		AEAB9F8A2E4894160091B83E /* location-demo */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "location-demo";
			sourceTree = "<group>";
		};
		AEAB9F9A2E4894180091B83E /* location-demoTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "location-demoTests";
			sourceTree = "<group>";
		};
		AEAB9FA42E4894180091B83E /* location-demoUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "location-demoUITests";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		AEAB9F852E4894160091B83E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AEAB9F942E4894180091B83E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AEAB9F9E2E4894180091B83E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		AEAB9F7F2E4894160091B83E = {
			isa = PBXGroup;
			children = (
				AEAB9F8A2E4894160091B83E /* location-demo */,
				AEAB9F9A2E4894180091B83E /* location-demoTests */,
				AEAB9FA42E4894180091B83E /* location-demoUITests */,
				AEAB9F892E4894160091B83E /* Products */,
			);
			sourceTree = "<group>";
		};
		AEAB9F892E4894160091B83E /* Products */ = {
			isa = PBXGroup;
			children = (
				AEAB9F882E4894160091B83E /* location-demo.app */,
				AEAB9F972E4894180091B83E /* location-demoTests.xctest */,
				AEAB9FA12E4894180091B83E /* location-demoUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		AEAB9F872E4894160091B83E /* location-demo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AEAB9FAB2E4894180091B83E /* Build configuration list for PBXNativeTarget "location-demo" */;
			buildPhases = (
				AEAB9F842E4894160091B83E /* Sources */,
				AEAB9F852E4894160091B83E /* Frameworks */,
				AEAB9F862E4894160091B83E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				AEAB9F8A2E4894160091B83E /* location-demo */,
			);
			name = "location-demo";
			packageProductDependencies = (
			);
			productName = "location-demo";
			productReference = AEAB9F882E4894160091B83E /* location-demo.app */;
			productType = "com.apple.product-type.application";
		};
		AEAB9F962E4894180091B83E /* location-demoTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AEAB9FAE2E4894180091B83E /* Build configuration list for PBXNativeTarget "location-demoTests" */;
			buildPhases = (
				AEAB9F932E4894180091B83E /* Sources */,
				AEAB9F942E4894180091B83E /* Frameworks */,
				AEAB9F952E4894180091B83E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				AEAB9F992E4894180091B83E /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				AEAB9F9A2E4894180091B83E /* location-demoTests */,
			);
			name = "location-demoTests";
			packageProductDependencies = (
			);
			productName = "location-demoTests";
			productReference = AEAB9F972E4894180091B83E /* location-demoTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		AEAB9FA02E4894180091B83E /* location-demoUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AEAB9FB12E4894180091B83E /* Build configuration list for PBXNativeTarget "location-demoUITests" */;
			buildPhases = (
				AEAB9F9D2E4894180091B83E /* Sources */,
				AEAB9F9E2E4894180091B83E /* Frameworks */,
				AEAB9F9F2E4894180091B83E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				AEAB9FA32E4894180091B83E /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				AEAB9FA42E4894180091B83E /* location-demoUITests */,
			);
			name = "location-demoUITests";
			packageProductDependencies = (
			);
			productName = "location-demoUITests";
			productReference = AEAB9FA12E4894180091B83E /* location-demoUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		AEAB9F802E4894160091B83E /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					AEAB9F872E4894160091B83E = {
						CreatedOnToolsVersion = 16.3;
					};
					AEAB9F962E4894180091B83E = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = AEAB9F872E4894160091B83E;
					};
					AEAB9FA02E4894180091B83E = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = AEAB9F872E4894160091B83E;
					};
				};
			};
			buildConfigurationList = AEAB9F832E4894160091B83E /* Build configuration list for PBXProject "location-demo" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = AEAB9F7F2E4894160091B83E;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = AEAB9F892E4894160091B83E /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				AEAB9F872E4894160091B83E /* location-demo */,
				AEAB9F962E4894180091B83E /* location-demoTests */,
				AEAB9FA02E4894180091B83E /* location-demoUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		AEAB9F862E4894160091B83E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AEAB9F952E4894180091B83E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AEAB9F9F2E4894180091B83E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		AEAB9F842E4894160091B83E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AEAB9F932E4894180091B83E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AEAB9F9D2E4894180091B83E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		AEAB9F992E4894180091B83E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = AEAB9F872E4894160091B83E /* location-demo */;
			targetProxy = AEAB9F982E4894180091B83E /* PBXContainerItemProxy */;
		};
		AEAB9FA32E4894180091B83E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = AEAB9F872E4894160091B83E /* location-demo */;
			targetProxy = AEAB9FA22E4894180091B83E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		AEAB9FA92E4894180091B83E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		AEAB9FAA2E4894180091B83E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		AEAB9FAC2E4894180091B83E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 9SBGQ83M2P;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "显示地图位置";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "显示地图位置";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.ffffy.location-demo";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		AEAB9FAD2E4894180091B83E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 9SBGQ83M2P;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "显示地图位置";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "显示地图位置";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.ffffy.location-demo";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		AEAB9FAF2E4894180091B83E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.ffffy.location-demoTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/location-demo.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/location-demo";
			};
			name = Debug;
		};
		AEAB9FB02E4894180091B83E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.ffffy.location-demoTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/location-demo.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/location-demo";
			};
			name = Release;
		};
		AEAB9FB22E4894180091B83E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.ffffy.location-demoUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "location-demo";
			};
			name = Debug;
		};
		AEAB9FB32E4894180091B83E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.ffffy.location-demoUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "location-demo";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		AEAB9F832E4894160091B83E /* Build configuration list for PBXProject "location-demo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AEAB9FA92E4894180091B83E /* Debug */,
				AEAB9FAA2E4894180091B83E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AEAB9FAB2E4894180091B83E /* Build configuration list for PBXNativeTarget "location-demo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AEAB9FAC2E4894180091B83E /* Debug */,
				AEAB9FAD2E4894180091B83E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AEAB9FAE2E4894180091B83E /* Build configuration list for PBXNativeTarget "location-demoTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AEAB9FAF2E4894180091B83E /* Debug */,
				AEAB9FB02E4894180091B83E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AEAB9FB12E4894180091B83E /* Build configuration list for PBXNativeTarget "location-demoUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AEAB9FB22E4894180091B83E /* Debug */,
				AEAB9FB32E4894180091B83E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = AEAB9F802E4894160091B83E /* Project object */;
}
