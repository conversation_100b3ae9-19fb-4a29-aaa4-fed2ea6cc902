//
//  MapView.swift
//  location-demo
//
//  Created by <PERSON><PERSON> on 2025/8/10.
//


import SwiftUI
import MapKit
import CoreLocation

/// 为系统类型 CLLocationCoordinate2D 添加 Equatable 扩展
extension CLLocationCoordinate2D: @retroactive Equatable {
    public static func == (lhs: CLLocationCoordinate2D, rhs: CLLocationCoordinate2D) -> Bool {
        lhs.latitude == rhs.latitude && lhs.longitude == rhs.longitude
    }
}

//轨迹点
struct TrailPoint: Identifiable{
    let id = UUID()
    let coordinate: CLLocationCoordinate2D
}




              
struct MapView: View {
    @ObservedObject var locationManager: UserLocationManager
    private let defaultLocation = CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
    
    @State private var hasCentered = false

    /// 采用 MapCameraPosition 替代传统 MKCoordinateRegion
    @State private var cameraPosition: MapCameraPosition = .region(
        MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074),
            span: MKCoordinateSpan(latitudeDelta: 0.01, longitudeDelta: 0.01)
        )
    )
    

    var body: some View {
        let trailPoints: [TrailPoint] = {
            guard let loc = locationManager.userLocation else { return [] }
            
            let offsets: [(Double, Double)] = [
                (0.0002, 0.0000),
                (0.0004, 0.0003),
                (0.0006, 0.0006),
                (0.0008, 0.0002)
            ]
            return offsets.map { offset in
                TrailPoint(coordinate: CLLocationCoordinate2D(
                    latitude: loc.latitude + offset.0,
                    longitude: loc.longitude + offset.1
                ))
            }
        }()
        
        // 用 Annotation 构建内容：支持标记和自定义内容
        Map(position: $cameraPosition) {
            if let userLoc = locationManager.userLocation {
                // 使用 Annotation 来标记用户当前位置信息
                Annotation("我在这里", coordinate: userLoc, anchor: .center) {
                    Image(systemName: "location.fill")
                        .foregroundColor(.blue)
                        .padding(6)
                        .background(Circle().fill(.white))
                }
            }
            
            // 轨迹标记
            ForEach(trailPoints) { point in
                Annotation("", coordinate: point.coordinate, anchor: .center){
                    Image(systemName: "shoeprints.fill")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 18, height: 18)
                        .foregroundStyle(.green)
                }
            }
        }
        .onAppear {
            // 初始时设置地图镜头中心为用户位置（或默认位置）
            if let userLoc = locationManager.userLocation {
                cameraPosition = .region(
                    MKCoordinateRegion(center: userLoc, span: MKCoordinateSpan(latitudeDelta: 0.01, longitudeDelta: 0.01))
                )
            }
        }
        .mapStyle(.standard(elevation: .realistic, pointsOfInterest: .including([.publicTransport]), showsTraffic: false))
        .mapControls {
            MapUserLocationButton()
            MapCompass()
            MapScaleView()
            MapPitchToggle()
        }
        .onChange(of: locationManager.userLocation) { oldValue, newValue in
            if let loc = newValue, !hasCentered {
                cameraPosition = .region(
                    MKCoordinateRegion(
                        center: loc,
                        span: MKCoordinateSpan(latitudeDelta: 0.01, longitudeDelta: 0.01)
                    )
                )
            }
            hasCentered = true
        }
        .edgesIgnoringSafeArea(.all)
    }
}


struct UserLocation: Identifiable {
    let id = UUID()
    let coordinate: CLLocationCoordinate2D
}

#Preview {
    // 模拟一个假的定位管理器
    class MockLocationManager: UserLocationManager {
        override init() {
            super.init()
            // 提供一个模拟位置，例如北京天安门
            self.userLocation = CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
        }
    }

    return MapView(locationManager: MockLocationManager())
}
